# 微信小程序二维码配置说明

## 问题描述
分享页面无法生成二维码，需要配置微信小程序的 AppID 和 Secret。

## 解决方案

### 1. 获取微信小程序配置
1. 登录微信公众平台：https://mp.weixin.qq.com/
2. 进入小程序管理后台
3. 在"开发" -> "开发管理" -> "开发设置"中找到：
   - AppID (小程序ID)
   - AppSecret (小程序密钥)

### 2. 在微信云托管中配置环境变量
1. 登录微信云托管控制台
2. 进入项目的"环境变量"设置
3. 添加以下环境变量：
   ```
   WX_APPID=你的小程序AppID
   WX_SECRET=你的小程序AppSecret
   ```

### 3. 重新部署服务
配置环境变量后，需要重新部署后端服务使配置生效。

## 验证配置
部署完成后，可以通过以下方式验证：

1. 查看后端日志，确认没有"缺少微信小程序配置"的错误
2. 在小程序中测试分享功能，查看二维码是否能正常生成

## 注意事项
1. AppSecret 是敏感信息，请妥善保管
2. 确保小程序已发布，否则二维码可能无法正常跳转
3. 二维码生成的落地页是 `pages/auth/auth`，确保该页面存在且能正常处理推荐参数

## 故障排查
如果仍然无法生成二维码，请检查：

1. 环境变量是否正确配置
2. 微信小程序是否有生成二维码的权限
3. 网络连接是否正常
4. 后端日志中的具体错误信息
