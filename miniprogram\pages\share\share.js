const app = getApp();

Page({
  data: {
    nickname: '',
    avatarUrl: '',
    userId: '',
    qrcodeUrl: '',
    shareImage: '/images/share/share.jpg',
    generatedImage: '', // canvas合成后的图片路径
    drawing: false,
    qrcodeRetryCount: 0, // 二维码生成重试次数
    maxRetryCount: 3 // 最大重试次数
  },
  onLoad() {
    // 获取用户信息，优先nickname和avatar
    const userInfo = app.globalData.userInfo || {};
    const nickname = userInfo.nickname || userInfo.nickName || '用户昵称';
    const avatarUrl = userInfo.avatar || userInfo.avatarUrl || '/images/icons2/默认头像.png';
    this.setData({
      nickname,
      avatarUrl,
      userId: userInfo._id || userInfo.id || ''
    });
    // 获取带推荐参数的小程序码
    this.generateQrcode();
  },
  generateQrcode() {
    const referrerId = this.data.userId;
    if (!referrerId) {
      wx.showToast({ title: '用户信息获取失败', icon: 'none' });
      return;
    }

    // 检查重试次数
    if (this.data.qrcodeRetryCount >= this.data.maxRetryCount) {
      wx.showToast({ title: '二维码生成失败，请稍后重试', icon: 'none' });
      return;
    }

    const retryText = this.data.qrcodeRetryCount > 0 ? `重试中(${this.data.qrcodeRetryCount}/${this.data.maxRetryCount})...` : '生成二维码中...';
    wx.showLoading({ title: retryText, mask: true });

    // 使用云托管方式调用API
    const envId = 'prod-5geioww562624006';
    const serviceName = 'lieyouqi';

    wx.cloud.callContainer({
      config: {
        env: envId
      },
      path: '/api/system/qrcode',
      method: 'GET',
      header: {
        'X-WX-SERVICE': serviceName,
        'content-type': 'application/json'
      },
      data: { scene: referrerId },
      success: (res) => {
        wx.hideLoading();
        console.log('二维码生成响应:', res);

        if (res.statusCode === 200 && res.data && res.data.success && res.data.qrcodeUrl) {
          // 构建完整的二维码URL
          const baseUrl = 'https://lieyouqi-158837-8-1258719867.sh.run.tcloudbase.com';
          const fullQrcodeUrl = res.data.qrcodeUrl.startsWith('http')
            ? res.data.qrcodeUrl
            : baseUrl + res.data.qrcodeUrl;

          console.log('二维码URL构建完成:', fullQrcodeUrl);
          this.setData({ qrcodeUrl: fullQrcodeUrl }, () => {
            this.drawShareImage();
          });
        } else {
          console.error('二维码生成失败:', res.data);
          let errorMessage = '二维码生成失败';

          if (res.data && res.data.message) {
            errorMessage = res.data.message;
          } else if (res.statusCode !== 200) {
            errorMessage = `服务器错误 (${res.statusCode})`;
          }

          this.handleQrcodeError(errorMessage);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('二维码生成请求失败:', err);

        let errorMessage = '网络请求失败';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请检查网络连接';
          } else if (err.errMsg.includes('fail')) {
            errorMessage = '网络连接失败，请稍后重试';
          }
        }

        this.handleQrcodeError(errorMessage);
      }
    });
  },

  // 处理二维码生成错误
  handleQrcodeError(message) {
    const retryCount = this.data.qrcodeRetryCount + 1;
    this.setData({ qrcodeRetryCount: retryCount });

    // 如果是配置错误，不进行重试
    if (message.includes('服务配置错误') || message.includes('微信服务认证失败')) {
      this.setData({ qrcodeUrl: null });
      wx.showModal({
        title: '二维码生成失败',
        content: message + '\n\n请联系管理员检查微信小程序配置',
        showCancel: false,
        confirmText: '我知道了'
      });
      return;
    }

    if (retryCount < this.data.maxRetryCount) {
      // 自动重试，延迟1秒
      console.log(`二维码生成失败，正在进行第${retryCount}次重试...`);
      setTimeout(() => {
        this.generateQrcode();
      }, 1000);
    } else {
      // 达到最大重试次数，设置为失败状态
      this.setData({ qrcodeUrl: null });
      wx.showModal({
        title: '二维码生成失败',
        content: `${message}\n\n已重试${this.data.maxRetryCount}次，请稍后再试或联系客服`,
        showCancel: true,
        cancelText: '稍后再试',
        confirmText: '手动重试',
        success: (res) => {
          if (res.confirm) {
            this.retryGenerateQrcode();
          }
        }
      });
    }
  },

  async drawShareImage() {
    if (this.data.drawing) return;
    this.setData({ drawing: true });

    wx.showLoading({ title: '生成分享图片...', mask: true });

    try {
      // 确保所有图片都能正确加载
      const [mainImg, avatarImg, qrcodeImg] = await Promise.all([
        this.getLocalImg(this.data.shareImage),
        this.getLocalImg(this.data.avatarUrl),
        this.getLocalImg(this.data.qrcodeUrl)
      ]);

      const ctx = wx.createCanvasContext('shareCanvas', this);

      // 设置画布背景为白色
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, 600, 800);

      // 绘制主图片（顶部背景图）
      ctx.drawImage(mainImg, 0, 0, 600, 300);

      // 绘制圆形头像
      ctx.save();
      ctx.beginPath();
      ctx.arc(70, 370, 40, 0, 2 * Math.PI);
      ctx.clip();
      ctx.drawImage(avatarImg, 30, 330, 80, 80);
      ctx.restore();

      // 绘制用户昵称
      ctx.setFontSize(24);
      ctx.setFillStyle('#333333');
      ctx.setTextAlign('left');
      const nicknameX = 130;
      const nicknameY = 385;
      ctx.fillText(this.data.nickname, nicknameX, nicknameY);

      // 绘制"向您推荐"文字
      ctx.setFontSize(20);
      ctx.setFillStyle('#888888');
      const nicknameWidth = ctx.measureText(this.data.nickname).width;
      ctx.fillText('向您推荐', nicknameX + nicknameWidth + 10, nicknameY);

      // 绘制二维码
      ctx.drawImage(qrcodeImg, 470, 330, 100, 100);

      // 绘制标语
      ctx.setFontSize(28);
      ctx.setFillStyle('#222222');
      ctx.setTextAlign('center');
      ctx.fillText('助力优质企业资产流转', 300, 480);

      // 绘制底部说明文字
      ctx.setFontSize(16);
      ctx.setFillStyle('#666666');
      ctx.fillText('扫码加入猎优企', 300, 520);

      // 完成绘制
      ctx.draw(false, () => {
        setTimeout(() => {
          wx.canvasToTempFilePath({
            canvasId: 'shareCanvas',
            width: 600,
            height: 800,
            destWidth: 600,
            destHeight: 800,
            success: (res) => {
              wx.hideLoading();
              this.setData({
                generatedImage: res.tempFilePath,
                drawing: false
              });
              console.log('分享图片生成成功:', res.tempFilePath);
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('生成分享图片失败:', err);
              wx.showToast({ title: '生成图片失败', icon: 'none' });
              this.setData({ drawing: false });
            }
          }, this);
        }, 500); // 延迟500ms确保绘制完成
      });
    } catch (e) {
      wx.hideLoading();
      console.error('绘制分享图片出错:', e);
      wx.showToast({ title: '图片加载失败', icon: 'none' });
      this.setData({ drawing: false });
    }
  },
  getLocalImg(src) {
    return new Promise((resolve, reject) => {
      if (!src) return reject('无图片');
      wx.getImageInfo({
        src,
        success: res => resolve(res.path),
        fail: err => reject(err)
      });
    });
  },
  onSaveImage() {
    if (!this.data.generatedImage) {
      if (this.data.drawing) {
        wx.showToast({ title: '图片生成中，请稍候...', icon: 'loading' });
      } else if (!this.data.qrcodeUrl) {
        // 如果二维码还没生成，提示用户等待
        wx.showToast({ title: '二维码生成中，请稍候...', icon: 'loading' });
      } else {
        // 如果没有生成图片且不在绘制中，重新生成
        wx.showToast({ title: '正在重新生成图片...', icon: 'loading' });
        this.drawShareImage();
      }
      return;
    }

    // 检查相册权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限，引导用户手动开启
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启权限',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      this.saveImageToAlbum();
                    }
                  }
                });
              }
            }
          });
        } else {
          // 有权限或未授权过，直接保存
          this.saveImageToAlbum();
        }
      },
      fail: () => {
        // 获取设置失败，直接尝试保存
        this.saveImageToAlbum();
      }
    });
  },

  // 保存图片到相册
  saveImageToAlbum() {
    wx.showLoading({ title: '保存中...', mask: true });

    wx.saveImageToPhotosAlbum({
      filePath: this.data.generatedImage,
      success: () => {
        wx.hideLoading();
        // 记录保存图片的分享行为
        this.recordShareAction('save_image');
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('保存图片失败:', err);

        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '保存失败',
            content: '需要您授权访问相册才能保存图片',
            confirmText: '重新授权',
            success: (res) => {
              if (res.confirm) {
                // 重新请求权限
                wx.authorize({
                  scope: 'scope.writePhotosAlbum',
                  success: () => {
                    this.saveImageToAlbum();
                  },
                  fail: () => {
                    wx.showToast({ title: '授权失败，无法保存图片', icon: 'none' });
                  }
                });
              }
            }
          });
        } else {
          wx.showToast({
            title: err.errMsg || '保存失败',
            icon: 'none'
          });
        }
      }
    });
  },
  onShareAppMessage() {
    if (!this.data.generatedImage) {
      if (!this.data.qrcodeUrl) {
        wx.showToast({ title: '二维码生成中，请稍候...', icon: 'loading' });
      } else {
        wx.showToast({ title: '图片生成中，请稍候...', icon: 'loading' });
      }
      return {};
    }

    // 记录分享行为
    this.recordShareAction('wechat');

    return {
      title: '向你推荐猎优企，助力优质企业资产流转',
      path: `/pages/auth/auth?scene=${this.data.userId}`,
      imageUrl: this.data.generatedImage
    };
  },

  // 记录分享行为
  recordShareAction(shareType = 'unknown') {
    try {
      const shareData = {
        userId: this.data.userId,
        shareType: shareType, // 'wechat', 'save_image'
        timestamp: new Date().toISOString(),
        nickname: this.data.nickname
      };

      // 可以在这里添加分享统计逻辑，比如调用后端API记录分享数据
      console.log('用户进行了分享操作:', shareData);

      // 显示分享成功提示
      const successText = shareType === 'save_image' ? '图片保存成功' : '分享成功';
      wx.showToast({
        title: successText,
        icon: 'success',
        duration: 1500
      });

      // 调用后端API记录分享统计
      this.callShareStatisticsAPI(shareData);
    } catch (e) {
      console.error('记录分享行为失败:', e);
    }
  },

  // 调用后端API记录分享统计
  callShareStatisticsAPI(shareData) {
    const envId = 'prod-5geioww562624006';
    const serviceName = 'lieyouqi';

    wx.cloud.callContainer({
      config: {
        env: envId
      },
      path: '/api/share/record',
      method: 'POST',
      header: {
        'X-WX-SERVICE': serviceName,
        'content-type': 'application/json'
      },
      data: shareData,
      success: (res) => {
        console.log('分享统计记录成功:', res);
      },
      fail: (err) => {
        console.error('分享统计记录失败:', err);
        // 分享统计失败不影响用户体验，只记录错误
      }
    });
  },

  // 重试生成二维码
  retryGenerateQrcode() {
    if (this.data.qrcodeUrl === null) {
      console.log('手动重试生成二维码');
      this.setData({
        qrcodeUrl: '', // 重置状态
        qrcodeRetryCount: 0 // 重置重试次数
      });
      this.generateQrcode();
    }
  }
});