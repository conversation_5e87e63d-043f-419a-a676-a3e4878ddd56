/**
 * 二维码生成测试脚本
 * 用于验证微信小程序配置是否正确
 */

require('dotenv').config();
const qrcodeService = require('./services/qrcodeService');

async function testQrcodeGeneration() {
  console.log('=== 二维码生成测试 ===');
  
  // 检查环境变量
  console.log('\n1. 检查环境变量配置:');
  console.log('- WX_APPID:', process.env.WX_APPID ? '已配置' : '未配置');
  console.log('- WX_SECRET:', process.env.WX_SECRET ? '已配置' : '未配置');
  
  if (!process.env.WX_APPID || !process.env.WX_SECRET) {
    console.error('\n❌ 微信小程序配置缺失！');
    console.log('请在环境变量中配置 WX_APPID 和 WX_SECRET');
    return;
  }
  
  // 测试二维码生成
  console.log('\n2. 测试二维码生成:');
  try {
    const testScene = 'test_user_123';
    console.log('测试场景参数:', testScene);
    
    const qrcodeUrl = await qrcodeService.generateQrcode(testScene);
    console.log('✅ 二维码生成成功!');
    console.log('二维码URL:', qrcodeUrl);
    
  } catch (error) {
    console.error('❌ 二维码生成失败:');
    console.error('错误信息:', error.message);
    
    if (error.message.includes('access_token')) {
      console.log('\n可能的原因:');
      console.log('- AppID 或 AppSecret 配置错误');
      console.log('- 网络连接问题');
      console.log('- 微信服务器暂时不可用');
    }
  }
  
  console.log('\n=== 测试完成 ===');
}

// 运行测试
testQrcodeGeneration().catch(console.error);
