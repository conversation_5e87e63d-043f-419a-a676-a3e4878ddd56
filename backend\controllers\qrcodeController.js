const qrcodeService = require('../services/qrcodeService');

exports.getQrcode = async (req, res) => {
  try {
    console.log('收到二维码生成请求，参数:', req.query);

    const { scene } = req.query;
    if (!scene) {
      console.error('缺少scene参数');
      return res.status(400).json({
        success: false,
        message: '缺少scene参数，请提供推荐人ID'
      });
    }

    console.log('开始生成二维码，scene:', scene);
    const qrcodeUrl = await qrcodeService.generateQrcode(scene);

    console.log('二维码生成成功:', qrcodeUrl);
    res.json({
      success: true,
      qrcodeUrl,
      scene: scene
    });
  } catch (error) {
    console.error('生成二维码失败:', error.message);
    console.error('错误堆栈:', error.stack);

    // 根据错误类型返回不同的状态码和消息
    let statusCode = 500;
    let message = '生成二维码失败';

    if (error.message.includes('微信小程序配置缺失')) {
      statusCode = 500;
      message = '服务配置错误，请联系管理员';
    } else if (error.message.includes('access_token')) {
      statusCode = 500;
      message = '微信服务认证失败，请稍后重试';
    } else if (error.message.includes('超时')) {
      statusCode = 504;
      message = '请求超时，请稍后重试';
    }

    res.status(statusCode).json({
      success: false,
      message: message,
      error: error.message,
      scene: req.query.scene
    });
  }
};