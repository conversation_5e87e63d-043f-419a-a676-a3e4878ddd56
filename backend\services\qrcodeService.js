const path = require('path');
const fs = require('fs');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// 微信小程序配置 - 从环境变量获取
const appId = process.env.WX_APPID || process.env.WECHAT_APPID;
const appSecret = process.env.WX_SECRET || process.env.WECHAT_SECRET;
const qrcodeDir = path.join(__dirname, '../public/qrcode');
const qrcodeBaseUrl = '/public/qrcode/';

// 确保二维码目录存在
if (!fs.existsSync(qrcodeDir)) {
  fs.mkdirSync(qrcodeDir, { recursive: true });
}

// 验证必要的环境变量
if (!appId || !appSecret) {
  console.error('缺少微信小程序配置:');
  console.error('- WX_APPID:', appId ? '已配置' : '未配置');
  console.error('- WX_SECRET:', appSecret ? '已配置' : '未配置');
}

async function getAccessToken() {
  // 验证配置
  if (!appId || !appSecret) {
    throw new Error('微信小程序配置缺失，请检查 WX_APPID 和 WX_SECRET 环境变量');
  }

  const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`;
  console.log('正在获取微信access_token...');

  try {
    const res = await axios.get(url);
    console.log('微信API响应:', res.data);

    if (res.data && res.data.access_token) {
      console.log('access_token获取成功');
      return res.data.access_token;
    }

    console.error('获取access_token失败:', res.data);
    throw new Error('获取access_token失败: ' + JSON.stringify(res.data));
  } catch (error) {
    console.error('请求微信API失败:', error.message);
    throw new Error('请求微信API失败: ' + error.message);
  }
}

exports.generateQrcode = async (scene) => {
  console.log('开始生成二维码，scene参数:', scene);

  if (!scene) {
    throw new Error('scene参数不能为空');
  }

  try {
    const accessToken = await getAccessToken();
    const qrcodeApi = `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${accessToken}`;
    const filename = `${scene}_${uuidv4()}.png`;
    const filePath = path.join(qrcodeDir, filename);
    const fileUrl = qrcodeBaseUrl + filename;

    // 生成小程序码的参数
    const postData = {
      scene: String(scene), // 确保scene是字符串
      page: 'pages/auth/auth', // 新用户落地页
      width: 430,
      auto_color: false,
      line_color: {"r":"0","g":"0","b":"0"},
      is_hyaline: false
    };

    console.log('请求微信小程序码API，参数:', postData);

    let response;
    try {
      response = await axios({
        url: qrcodeApi,
        method: 'POST',
        responseType: 'arraybuffer',
        data: postData,
        timeout: 30000 // 30秒超时
      });
    } catch (err) {
      console.error('请求微信小程序码API失败:', err.response ? err.response.data : err.message);

      // 如果是网络错误，提供更详细的信息
      if (err.code === 'ECONNABORTED') {
        throw new Error('请求微信API超时，请稍后重试');
      }

      throw new Error('请求微信小程序码API失败: ' + (err.response ? JSON.stringify(err.response.data) : err.message));
    }

    console.log('微信小程序码API响应状态:', response.status);
    console.log('响应头Content-Type:', response.headers['content-type']);

    // 检查返回内容是否为图片
    if (response.headers['content-type'] && response.headers['content-type'].includes('image')) {
      // 保存图片文件
      fs.writeFileSync(filePath, response.data);
      console.log('二维码文件保存成功:', filePath);
      console.log('二维码URL:', fileUrl);
      return fileUrl;
    } else {
      // 不是图片，可能是错误信息
      const text = response.data.toString('utf8');
      console.error('微信小程序码API返回非图片内容:', text);
      console.error('请求参数:', postData);

      // 尝试解析错误信息
      try {
        const errorData = JSON.parse(text);
        throw new Error(`微信小程序码生成失败: ${errorData.errmsg || errorData.message || text}`);
      } catch (parseError) {
        throw new Error('微信小程序码生成失败: ' + text);
      }
    }
  } catch (error) {
    console.error('生成二维码过程中发生错误:', error.message);
    throw error;
  }
};